# Database Seeding Scripts

This directory contains scripts for seeding the BITS DataScience Platform database with sample data for development and testing purposes.

## Overview

The seeding system provides:
- **Sample Users**: Instructors, Teaching Assistants, and Students
- **Sample Courses**: Multiple courses with different configurations
- **Sample Projects**: 5 comprehensive projects covering various data science topics
- **Proper Relationships**: All foreign key constraints are maintained
- **Idempotent Operations**: Safe to run multiple times without creating duplicates

## Files

### Core Seeding Scripts

- **`src/seeders/004-sample-projects.js`** - Main seeding script for projects and related data
- **`scripts/seed-projects.js`** - Wrapper script with enhanced output and error handling
- **`scripts/verify-seeded-data.js`** - Verification script to check seeded data integrity

### Configuration

- **`src/config/database.js`** - Database configuration for Sequelize CLI
- **`.sequelizerc`** - Sequelize CLI configuration file

## Sample Data Created

### Users (5 total)
- **Dr. <PERSON>** (Instructor) - Computer Science Department
- **Dr. <PERSON>** (Instructor) - Data Science Department  
- **<PERSON>** (Teaching Assistant) - Computer Science Department
- **<PERSON>** (Teaching Assistant) - Data Science Department
- **<PERSON>** (Student) - 3rd year Computer Science major

### Courses (3 total)
- **CS501** - Introduction to Data Science (Fall 2024)
- **CS502** - Machine Learning Fundamentals (Fall 2024)
- **CS503** - Advanced Analytics (Fall 2024)

### Projects (5 total)

1. **Sample Project 1: Data Exploration with Pandas**
   - Course: CS501 (Introduction to Data Science)
   - Difficulty: Beginner
   - Duration: 8 hours
   - Status: Published

2. **Sample Project 2: Machine Learning Classification**
   - Course: CS502 (Machine Learning Fundamentals)
   - Difficulty: Intermediate
   - Duration: 12 hours
   - Status: Published

3. **Sample Project 3: Time Series Analysis**
   - Course: CS503 (Advanced Analytics)
   - Difficulty: Advanced
   - Duration: 15 hours
   - Status: Published

4. **Sample Project 4: Deep Learning with Neural Networks**
   - Course: CS502 (Machine Learning Fundamentals)
   - Difficulty: Advanced
   - Duration: 20 hours
   - Status: Draft

5. **Sample Project 5: Data Visualization Dashboard**
   - Course: CS503 (Advanced Analytics)
   - Difficulty: Intermediate
   - Duration: 16 hours
   - Status: Published

## Usage

### Running the Seeding Scripts

#### Option 1: Using Sequelize CLI (Recommended)
```bash
# Run the specific seeder
npx sequelize-cli db:seed --seed 004-sample-projects.js

# Run all seeders
npm run db:seed
```

#### Option 2: Using the Enhanced Script
```bash
# Run with enhanced output and verification
node scripts/seed-projects.js
```

### Verifying Seeded Data
```bash
# Run verification script
node scripts/verify-seeded-data.js
```

### Removing Seeded Data
```bash
# Remove the specific seeder data
npx sequelize-cli db:seed:undo --seed 004-sample-projects.js

# Remove all seeder data
npm run db:seed:undo
```

## Features

### Idempotent Operations
The seeding scripts check for existing data before inserting new records:
```javascript
const existingProjects = await queryInterface.sequelize.query(
  "SELECT COUNT(*) as count FROM projects WHERE title LIKE 'Sample Project%'",
  { type: Sequelize.QueryTypes.SELECT, transaction }
);

if (existingProjects[0].count > 0) {
  console.log('Sample projects already exist, skipping seeding...');
  return;
}
```

### Transaction Safety
All operations are wrapped in database transactions:
```javascript
const transaction = await queryInterface.sequelize.transaction();
try {
  // Seeding operations
  await transaction.commit();
} catch (error) {
  await transaction.rollback();
  throw error;
}
```

### Referential Integrity
Data is inserted in the correct order to maintain foreign key relationships:
1. Users (referenced by courses and projects)
2. Courses (referenced by projects)
3. Projects (references users and courses)

### Comprehensive Data
Each project includes:
- Complete metadata (title, description, instructions)
- Learning objectives and prerequisites
- File URLs (templates, datasets, resources)
- Grading configuration
- Collaboration settings
- Due dates and submission policies

## Database Schema Compatibility

The seeding scripts are designed to work with the actual database schema:
- Uses correct column names and data types
- Respects JSONB fields for arrays and objects
- Handles ENUM values properly
- Maintains foreign key constraints

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   ```
   Solution: Ensure PostgreSQL is running and connection details are correct
   ```

2. **Foreign Key Constraint Violations**
   ```
   Solution: The scripts handle this automatically by creating dependencies first
   ```

3. **Duplicate Data**
   ```
   Solution: Scripts are idempotent - they check for existing data before inserting
   ```

### Verification Commands

Check if data was inserted correctly:
```sql
-- Count sample projects
SELECT COUNT(*) FROM projects WHERE title LIKE 'Sample Project%';

-- Check relationships
SELECT p.title, c.name as course_name, u.name as creator_name 
FROM projects p 
JOIN courses c ON p.course_id = c.id 
JOIN users u ON p.created_by = u.id 
WHERE p.title LIKE 'Sample Project%';
```

## Development Notes

### Adding New Sample Data

To add new sample projects:
1. Edit `src/seeders/004-sample-projects.js`
2. Add new project objects to the `projects` array
3. Ensure all required fields are included
4. Test with `node scripts/verify-seeded-data.js`

### Schema Changes

If the database schema changes:
1. Update the seeding script to match new column names/types
2. Update the verification script queries
3. Test thoroughly in development environment

## Integration with Development Workflow

The seeding scripts integrate with the standard development setup:

```bash
# Full development setup
npm run setup  # Includes db:create, db:migrate, and db:seed

# Reset database with fresh data
npm run db:reset  # Includes undo, migrate, and seed
```

This provides a consistent development environment with realistic test data for all team members.
