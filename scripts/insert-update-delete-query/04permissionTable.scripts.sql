-- CREATE EXTENSION IF NOT EXISTS pgcrypto;

INSERT INTO public.permissions
  (id, key, name, description, category, is_system_permission, created_at, updated_at, deleted_at)
VALUES
    (gen_random_uuid(), 'create_projects',  'Create Projects',  'Allow creating new projects',                 'projects',  TRUE, NOW(), NOW(), NULL),
    (gen_random_uuid(), 'view_projects',    'View Projects',    'Allow viewing project lists and details',     'projects',  TRUE, NOW(), NOW(), NULL),
    (gen_random_uuid(), 'edit_projects',    'Edit Projects',    'Allow editing existing projects',             'projects',  TRUE, NOW(), NOW(), NULL),
    (gen_random_uuid(), 'delete_projects',  'Delete Projects',  'Allow deleting projects',                     'projects',  TRUE, NOW(), NOW(), NULL),
    (gen_random_uuid(), 'view_courses',     'View Courses',     'Allow viewing course catalog and details',    'courses',   TRUE, NOW(), NOW(), NULL),
    (gen_random_uuid(), 'manage_enrollments','Manage Enrollments','Allow adding/removing course enrollments', 'enrollment',TRUE, NOW(), NOW(), NULL),
    (gen_random_uuid(), 'project:read',     'Project Read',     'Read access to project resources (API scope)','project',   TRUE, NOW(), NOW(), NULL),
    (gen_random_uuid(), 'project:create',   'Project Create',   'Create access to project resources (API scope)','project', TRUE, NOW(), NOW(), NULL),
    (gen_random_uuid(), 'project:update',   'Project Update',   'Update access to project resources (API scope)','project', TRUE, NOW(), NOW(), NULL),
    (gen_random_uuid(), 'publish_projects',  'Projects publish',  'Allow creating new projects', 'projects',  TRUE, NOW(), NOW(), NULL);