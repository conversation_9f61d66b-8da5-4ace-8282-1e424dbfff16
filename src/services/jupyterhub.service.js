// services/jupyterService.js
import config from '../config/database.config.js';
import axios from 'axios';
import logger from '../config/logger.config.js';
import NodeCache from 'node-cache';
import ApiError from '../utils/ApiError.utils.js';
import httpStatus from 'http-status';
import jupyterExecutionService from './jupyterExecution.service.js';

const userToken = new NodeCache({ stdTTL: 5 * 60 });
const JUPYTERHUB_URL = `${config.jupyterhub.url}/hub/api`;

// ------------------ Axios Client ------------------
const jupyterhubApi = axios.create({
  baseURL: JUPYTERHUB_URL,
  timeout: 60000,
  headers: {
    Authorization: `token ${config.jupyterhub.apiToken}`
  }
});

function getUserApi(username, token) {
  return axios.create({
    baseURL: `${config.jupyterhub.url}/user/${encodeURIComponent(username)}/api`,
    params: { token }
  });
}

const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

// ------------------ User Management ------------------
const getUser = async username => {
  try {
    const response = await jupyterhubApi.get(`/users/${username}`);
    return response.data;
  } catch (error) {
    if (error.response?.status === 404) {
      return null; // User not found
    }
    throw error;
  }
};

const createUser = async username => {
  try {
    const response = await jupyterhubApi.post(`/users/${username}`, {
      name: username
    });
    logger.info(`User '${username}' created successfully.`);
    return response.data;
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(`Failed to create user ${username}: ${errorMessage}`);
    throw new Error('Could not create Jupyter user.');
  }
};

// ------------------ Server Management ------------------
const getServerStatus = async username => {
  const user = await getUser(username);
  return user?.servers?.[''] || null;
};

const startServer = async (username, serverName = '') => {
  try {
    const response = await jupyterhubApi.post(
      `/users/${username}/servers/${serverName}`,
      { name: serverName }
    );
    logger.info(`JupyterHub server start initiated for user: ${username}`);
    return response.data;
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(
      `Failed to start JupyterHub server for ${username}: ${errorMessage}`
    );
    throw new Error('Could not start Jupyter server.');
  }
};

const stopServer = async user => {
  const username = user.jupiterUserName;
  try {
    const response = await jupyterhubApi.delete(`/users/${username}/server`);
    logger.info(`JupyterHub server stopped for user: ${username}`);
    return response.data;
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(
      `Failed to stop JupyterHub server for ${username}: ${errorMessage}`
    );
    throw new Error('Could not stop Jupyter server.');
  }
};

const getUserToken = async username => {
  try {
    const cachedToken = userToken.get(username);
    if (cachedToken) {
      logger.info(`Using cached token for user: ${username}`);
      return cachedToken;
    }
    const response = await jupyterhubApi.post(`/users/${username}/tokens`, {
      note: `User token for ${username}`
    });
    logger.info(
      `Token generated for user: ${username} -- ${JSON.stringify(response.data, null, 2)}`
    );
    const token = response.data.token;
    userToken.set(username, token);

    return token;
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(`Failed to generate token for ${username}: ${errorMessage}`);
    throw new Error('Could not generate user token.');
  }
};

// ------------------ Ensure Running Server ------------------
const ensureServerIsRunning = async username => {
  const POLL_INTERVAL = 2000; // 2 sec
  const START_TIMEOUT = 120000; // 2 min
  const startTime = Date.now();

  logger.info(`Ensuring Jupyter server is running for user: ${username}`);

  try {
    // 1. Ensure user exists
    let user = await getUser(username);
    if (!user) {
      user = await createUser(username);
    }

    // 2. Poll until server is ready
    while (Date.now() - startTime < START_TIMEOUT) {
      const serverStatus = await getServerStatus(username);

      if (serverStatus?.ready) {
        const userToken = await getUserToken(username);
        logger.info(`Server for '${username}' is ready.`);
        const iframeUrl = `${config.jupyterhub.url}/user/${encodeURIComponent(
          username
        )}/lab?token=${encodeURIComponent(userToken)}`;
        serverStatus.url = iframeUrl;
        return serverStatus; // ✅ Always return final server object
      }

      if (!serverStatus) {
        logger.info(`Server not found for '${username}'. Requesting start...`);
        await startServer(username, user.id);
      } else {
        logger.info(`Server for '${username}' is pending. Waiting...`);
      }

      await delay(POLL_INTERVAL);
    }

    throw new Error(`Server for ${username} did not become ready in time.`);
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(`JupyterHub API error for ${username}: ${errorMessage}`);
    throw new Error('Could not ensure Jupyter server is running.');
  }
};

// ------------------ Create Workspace ------------------
const createWorkspace = async (projectId, userDetails) => {
  try {
    if (!projectId) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'projectId is required');
    }
    const username = userDetails.jupiterUserName;
    const token = userDetails.jupyterUserToken;

    if (!username || !token) {
      throw new ApiError(
        httpStatus.UNAUTHORIZED,
        'Missing Jupyter user context'
      );
    }

    logger.info(
      `Creating workspace for project: ${projectId}, user: ${username}`
    );

    // 1) Ensure server is running
    const server = await ensureServerIsRunning(username);

    // User-scoped Jupyter API client (equivalent to proxy rewrite)
    const userApi = getUserApi(username, token);

    const folderPath = `${projectId}`; // relative to user root
    const notebookName = 'Untitled.ipynb';
    const notebookPath = `${folderPath}/${notebookName}`;

    // 2) Create folder /:projectId (idempotent)
    try {
      await userApi.put(`/contents/${encodeURIComponent(folderPath)}`, {
        type: 'directory'
      });
    } catch (e) {
      console.error(e);
      if (e.response?.status !== 409) {
        // 409 -> already exists; ignore
        throw e;
      }
    }

    // 3) Create notebook at /:projectId/Untitled.ipynb (idempotent-ish)
    const projectData = await userApi.get(
      `/contents/${encodeURIComponent(folderPath)}`
    );
    const notebookExists = projectData.data.content.some(
      file => file.type === 'notebook'
    );
    if (projectData.isSuccess && !notebookExists) {
      await userApi.put(`/contents/${encodeURIComponent(notebookPath)}`, {
        type: 'notebook',
        format: 'json',
        content: {
          cells: [],
          metadata: {},
          nbformat: 4,
          nbformat_minor: 5
        }
      });
    }

    // 4) Start a session bound to the notebook with kernel python3
    const sessionResp = await userApi.post('/sessions', {
      kernel: { name: 'python3' },
      name: '',
      type: 'notebook',
      path: notebookPath
    });

    const session = sessionResp.data;
    const kernel = session.kernel;

    return {
      folderPath: `/${folderPath}`,
      notebookPath: `/${notebookPath}`,
      server,
      session,
      kernel
    };
  } catch (error) {
    throw error;
  }
};

// ------------------ Execute Code ------------------
const executeCode = async (kernelId, code, options) => {
  try {
    if (!code) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Code is required');
    }

    logger.info(`Executing code in kernel: ${kernelId}`);

    const result = await jupyterExecutionService.executeCode(
      kernelId,
      code,
      options
    );

    return {
      kernel_id: kernelId,
      execution_count: result.execution_count,
      status: result.status,
      outputs: result.outputs,
      error: result.error || null
    };
  } catch (error) {
    throw error;
  }
};

// ------------------ Execute Notebook ------------------
const executeNotebook = async (kernelId, notebook, options) => {
  try {
    if (!notebook || !notebook.cells) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        'Notebook JSON with cells is required'
      );
    }

    logger.info(
      `Executing notebook with ${notebook.cells.length} cells in kernel: ${kernelId}`
    );

    const results = [];
    for (const [index, cell] of notebook.cells.entries()) {
      if (cell.cell_type !== 'code') continue; // Skip non-code cells

      const code = Array.isArray(cell.source)
        ? cell.source.join('')
        : String(cell.source || '');
      const cellOptions = {
        ...options,
        silent: false,
        store_history: true,
        user_expressions: {},
        allow_stdin: false,
        stop_on_error: options.stopOnError
      };

      try {
        const result = await jupyterExecutionService.executeCode(
          kernelId,
          code,
          cellOptions
        );
        results.push({
          cell_index: index,
          ...result
        });
      } catch (error) {
        results.push({
          cell_index: index,
          success: false,
          error: error.message
        });
        if (options.stopOnError) break; // Stop on first error if enabled
      }
    }

    return {
      kernel_id: kernelId,
      total_cells: results.length,
      results
    };
  } catch (error) {
    throw error;
  }
};

// ------------------ Exports ------------------
const jupyterService = {
  jupyterhubApi,
  ensureServerIsRunning,
  getUser,
  createUser,
  getServerStatus,
  startServer,
  stopServer,
  getUserToken,
  createWorkspace,
  executeCode,
  executeNotebook
};

export default jupyterService;
