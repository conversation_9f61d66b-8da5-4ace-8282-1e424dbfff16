import httpStatus from 'http-status';
import { generateToken } from '../middlewares/auth.middlewares.js';
import { User, Role, Permission } from '../models/associations.js';
import ApiError from '../utils/ApiError.utils.js';
import logger from '../config/logger.config.js';

class authService {
    /**
     * login 
     */
    async login(req) {
        const { email, password } = req.body;

        if (!email || !password) throw new ApiError(httpStatus.BAD_REQUEST, 'Email and password are required');

        // Find user with roles and permissions
        const user = await User.findOne({
            where: { email: email.toLowerCase() },
            include: [{
                model: Role,
                as: 'roles',
                include: [{
                    model: Permission,
                    as: 'permissions'
                }]
            }]
        });

        if (!user) throw new ApiError(httpStatus.BAD_REQUEST, 'No user found with this email address');
        if (user.status !== 'active') throw new ApiError(httpStatus.BAD_REQUEST, 'Your account is not active. Please contact administrator.');

        // Check password
        const isPasswordValid = await user.comparePassword(password);
        if (!isPasswordValid) throw new ApiError(httpStatus.BAD_REQUEST, 'Incorrect password');

        // Generate JWT token
        const token = generateToken(user);

        // Update last login
        await user.updateLastLogin();

        // Prepare user response (without sensitive data)
        const userResponse = {
            id: user.id,
            name: user.name,
            email: user.email,
            profilePicture: user.profile_picture,
            lastLogin: user.last_login,
            roles: user.roles?.map(role => ({
                id: role.id,
                name: role.name,
                permissions: role.permissions?.map(permission => permission.key) || []
            })) || []
        };

        logger.info(`User logged in: ${user.email}`);

        return {
            token,
            user: userResponse
        }
    }
}

export default new authService();