import logger from '../config/logger.config.js';
import { Project } from '../models/associations.js';
import ApiError from './ApiError.utils.js';
import httpStatus from 'http-status';

class enhancedProjectUtils {
  /**
   * check if project is exits or not
   */
  async checkProjectExist(projectId) {
    try {
      const project = await Project.findByPk(projectId);
      if (!project) {
        return false;
      }
      return project;
    } catch (error) {
      throw error;
    }
  }

  /**
   * update project details
   */
  async updateProjectDetails(projectId, updates) {
    try {
      const project = await Project.findByPk(projectId);
      if (!project) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project not found');
      }
      await project.update(updates);
      return project;
    } catch (error) {
      throw error;
    }
  }

  /**
   * update a project's dataset S3 URL
   * @param {string} s3Url - The S3 URL of the dataset
   * @param {string} projectId - The ID of the project to update
   * @returns {Promise<void>}
   */
  async updateDataSetS3Url(s3Url, projectId) {
    try {
      const project = await Project.findByPk(projectId);
      if (!project) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Project not found');
      }
      project.dataset_s3_url = s3Url;
      await project.save();
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check if a project belongs to a specific course
   * @param {UUID} courseId
   * @param {UUID} projectId
   * @returns
   */
  async checkCourseProjectId(courseId, projectId) {
    try {
      const project = await Project.findOne({
        where: { id: projectId, course_id: courseId }
      });
      if (!project) {
        logger.error(
          `Project ${projectId} does not belong to course ${courseId}`
        );
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project does not belong to the specified course'
        );
      }
      return true;
    } catch (error) {
      throw error;
    }
  }
}

export default new enhancedProjectUtils();
