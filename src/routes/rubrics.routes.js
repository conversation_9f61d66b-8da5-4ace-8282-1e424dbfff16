import express from 'express';
import {
  createRubric,
  getRubrics,
  getRubricById,
  updateRubric,
  deleteRubric,
  getRubricTemplates,
  duplicateRubricTemplate,
  getProjectRubrics,
  getRubricStats
} from '../controllers/rubric.controller.js';
import { jwtMiddleware } from '../middlewares/auth.middlewares.js';
import { requirePermissions } from '../middlewares/rbac.middlewares.js';
import { query, param, body } from 'express-validator';
import { validate } from '../middlewares/validation.middlewares.js';

const router = express.Router();

// Apply JWT middleware to all routes
router.use(jwtMiddleware);

/**
 * @swagger
 * components:
 *   schemas:
 *     Rubric:
 *       type: object
 *       required:
 *         - title
 *         - criteria
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique identifier for the rubric
 *         project_id:
 *           type: string
 *           format: uuid
 *           description: ID of the project (null for templates)
 *         title:
 *           type: string
 *           description: Title of the rubric
 *         description:
 *           type: string
 *           description: Description of the rubric
 *         criteria:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Name of the criterion
 *               description:
 *                 type: string
 *                 description: Description of the criterion
 *               points:
 *                 type: number
 *                 description: Maximum points for the criterion
 *         total_points:
 *           type: number
 *           default: 100
 *           description: Total points for the rubric
 *         grading_scale:
 *           type: object
 *           description: Letter grade scale mapping
 *         is_template:
 *           type: boolean
 *           default: false
 *           description: Whether this rubric can be used as a template
 *         template_name:
 *           type: string
 *           description: Name for template rubrics
 *         created_by:
 *           type: string
 *           format: uuid
 *           description: User who created the rubric
 *         checkpoint_mapping:
 *           type: object
 *           description: Mapping of rubric criteria to specific checkpoints
 *         project:
 *           $ref: '#/components/schemas/Project'
 *         creator:
 *           $ref: '#/components/schemas/User'
 */

/**
 * @swagger
 * /api/rubrics:
 *   post:
 *     summary: Create a new rubric
 *     tags: [Rubrics]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - criteria
 *             properties:
 *               project_id:
 *                 type: string
 *                 format: uuid
 *                 description: ID of the project (optional for templates)
 *               title:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 200
 *               description:
 *                 type: string
 *               criteria:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - name
 *                     - description
 *                     - points
 *                   properties:
 *                     name:
 *                       type: string
 *                     description:
 *                       type: string
 *                     points:
 *                       type: number
 *                       minimum: 0
 *               total_points:
 *                 type: number
 *                 default: 100
 *               grading_scale:
 *                 type: object
 *               is_template:
 *                 type: boolean
 *                 default: false
 *               template_name:
 *                 type: string
 *               checkpoint_mapping:
 *                 type: object
 *               metadata:
 *                 type: object
 *     responses:
 *       201:
 *         description: Rubric created successfully
 *       400:
 *         description: Invalid input data
 *       404:
 *         description: Project not found
 *       500:
 *         description: Internal server error
 */
router.post(
  '/',
  [
    requirePermissions(['create_projects']),
    body('title')
      .trim()
      .isLength({ min: 2, max: 200 })
      .withMessage('must be between 2 and 200 characters'),
    body('description')
      .optional()
      .trim()
      .notEmpty()
      .withMessage('Description cannot be empty'),
    body('criteria')
      .isArray({ min: 1 })
      .withMessage('Criteria must be a non-empty array'),
    body('criteria.*.name')
      .trim()
      .notEmpty()
      .withMessage('Each criterion must have a name'),
    body('criteria.*.description')
      .trim()
      .notEmpty()
      .withMessage('Each criterion must have a description'),
    body('criteria.*.points')
      .isFloat({ min: 0 })
      .withMessage('Each criterion must have positive points'),
    body('total_points')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Total points must be positive'),
    body('project_id')
      .optional()
      .isUUID()
      .withMessage('Valid project ID is required'),
    body('is_template')
      .optional()
      .isBoolean()
      .withMessage('is_template must be a boolean')
  ],
  validate,
  createRubric
);

/**
 * @swagger
 * /api/rubrics:
 *   get:
 *     summary: Get all rubrics with filtering
 *     tags: [Rubrics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: project_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by project ID
 *       - in: query
 *         name: created_by
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by creator
 *       - in: query
 *         name: is_template
 *         schema:
 *           type: boolean
 *         description: Filter by template status
 *       - in: query
 *         name: template_name
 *         schema:
 *           type: string
 *         description: Filter by template name (partial match)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *       - in: query
 *         name: sort_by
 *         schema:
 *           type: string
 *           default: created_at
 *         description: Field to sort by
 *       - in: query
 *         name: sort_order
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: DESC
 *         description: Sort order
 *     responses:
 *       200:
 *         description: Rubrics retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get(
  '/',
  [
    requirePermissions(['project:read']),
    query('project_id')
      .optional()
      .isUUID()
      .withMessage('Valid project ID is required'),
    query('created_by')
      .optional()
      .isUUID()
      .withMessage('Valid user ID is required'),
    query('is_template')
      .optional()
      .isBoolean()
      .withMessage('is_template must be a boolean'),
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('sort_by')
      .optional()
      .isString()
      .withMessage('Sort by must be a string'),
    query('sort_order')
      .optional()
      .isIn(['ASC', 'DESC'])
      .withMessage('Sort order must be ASC or DESC')
  ],
  validate,
  getRubrics
);

/**
 * @swagger
 * /api/rubrics/{id}:
 *   get:
 *     summary: Get a specific rubric
 *     tags: [Rubrics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Rubric ID
 *     responses:
 *       200:
 *         description: Rubric retrieved successfully
 *       404:
 *         description: Rubric not found
 *       500:
 *         description: Internal server error
 */
router.get(
  '/:id',
  [
    requirePermissions(['project:read']),
    param('id').isUUID().withMessage('Valid rubric ID is required')
  ],
  validate,
  getRubricById
);

/**
 * @swagger
 * /api/rubrics/{id}:
 *   put:
 *     summary: Update a rubric
 *     tags: [Rubrics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Rubric ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 200
 *               description:
 *                 type: string
 *               criteria:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     description:
 *                       type: string
 *                     points:
 *                       type: number
 *                       minimum: 0
 *               total_points:
 *                 type: number
 *                 minimum: 0
 *               grading_scale:
 *                 type: object
 *               is_template:
 *                 type: boolean
 *               template_name:
 *                 type: string
 *               checkpoint_mapping:
 *                 type: object
 *               metadata:
 *                 type: object
 *     responses:
 *       200:
 *         description: Rubric updated successfully
 *       403:
 *         description: Permission denied
 *       404:
 *         description: Rubric not found
 *       500:
 *         description: Internal server error
 */
router.put(
  '/:id',
  [
    requirePermissions(['project:update']),
    param('id').isUUID().withMessage('Valid rubric ID is required'),
    body('title')
      .optional()
      .trim()
      .isLength({ min: 2, max: 200 })
      .withMessage('Title must be between 2 and 200 characters'),
    body('description')
      .optional()
      .trim()
      .notEmpty()
      .withMessage('Description cannot be empty'),
    body('criteria')
      .optional()
      .isArray({ min: 1 })
      .withMessage('Criteria must be a non-empty array'),
    body('criteria.*.name')
      .optional()
      .trim()
      .notEmpty()
      .withMessage('Each criterion must have a name'),
    body('criteria.*.description')
      .optional()
      .trim()
      .notEmpty()
      .withMessage('Each criterion must have a description'),
    body('criteria.*.points')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Each criterion must have positive points'),
    body('total_points')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Total points must be positive'),
    body('is_template')
      .optional()
      .isBoolean()
      .withMessage('is_template must be a boolean')
  ],
  validate,
  updateRubric
);

/**
 * @swagger
 * /api/rubrics/{id}:
 *   delete:
 *     summary: Delete a rubric
 *     tags: [Rubrics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Rubric ID
 *     responses:
 *       200:
 *         description: Rubric deleted successfully
 *       400:
 *         description: Cannot delete rubric in use
 *       403:
 *         description: Permission denied
 *       404:
 *         description: Rubric not found
 *       500:
 *         description: Internal server error
 */
router.delete(
  '/:id',
  [
    requirePermissions(['project:delete']),
    param('id').isUUID().withMessage('Valid rubric ID is required')
  ],
  validate,
  deleteRubric
);

/**
 * @swagger
 * /api/rubrics/templates:
 *   get:
 *     summary: Get rubric templates
 *     tags: [Rubrics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: template_name
 *         schema:
 *           type: string
 *         description: Filter by template name (partial match)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Rubric templates retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get(
  '/templates',
  [
    requirePermissions(['project:read']),
    query('template_name')
      .optional()
      .isString()
      .withMessage('Template name must be a string'),
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ],
  validate,
  getRubricTemplates
);

/**
 * @swagger
 * /api/rubrics/{id}/duplicate:
 *   post:
 *     summary: Duplicate a rubric template for a project
 *     tags: [Rubrics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Rubric template ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               project_id:
 *                 type: string
 *                 format: uuid
 *                 description: ID of the project to assign the rubric to
 *               title:
 *                 type: string
 *                 description: Custom title for the duplicated rubric
 *               description:
 *                 type: string
 *                 description: Custom description for the duplicated rubric
 *     responses:
 *       201:
 *         description: Rubric template duplicated successfully
 *       400:
 *         description: Can only duplicate rubric templates
 *       404:
 *         description: Rubric template or project not found
 *       500:
 *         description: Internal server error
 */
router.post(
  '/:id/duplicate',
  [
    requirePermissions(['project:create']),
    param('id').isUUID().withMessage('Valid rubric ID is required'),
    body('project_id')
      .optional()
      .isUUID()
      .withMessage('Valid project ID is required'),
    body('title')
      .optional()
      .trim()
      .isLength({ min: 2, max: 200 })
      .withMessage('Title must be between 2 and 200 characters'),
    body('description')
      .optional()
      .trim()
      .notEmpty()
      .withMessage('Description cannot be empty')
  ],
  validate,
  duplicateRubricTemplate
);

/**
 * @swagger
 * /api/rubrics/project/{projectId}:
 *   get:
 *     summary: Get rubrics for a specific project
 *     tags: [Rubrics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Project ID
 *     responses:
 *       200:
 *         description: Project rubrics retrieved successfully
 *       404:
 *         description: Project not found
 *       500:
 *         description: Internal server error
 */
router.get(
  '/project/:projectId',
  [
    requirePermissions(['project:read']),
    param('projectId').isUUID().withMessage('Valid project ID is required')
  ],
  validate,
  getProjectRubrics
);

/**
 * @swagger
 * /api/rubrics/stats:
 *   get:
 *     summary: Get rubric statistics
 *     tags: [Rubrics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: created_by
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by creator
 *       - in: query
 *         name: is_template
 *         schema:
 *           type: boolean
 *         description: Filter by template status
 *     responses:
 *       200:
 *         description: Rubric statistics retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get(
  '/stats',
  [
    requirePermissions(['project:read']),
    query('created_by')
      .optional()
      .isUUID()
      .withMessage('Valid user ID is required'),
    query('is_template')
      .optional()
      .isBoolean()
      .withMessage('is_template must be a boolean')
  ],
  validate,
  getRubricStats
);

export default router;
