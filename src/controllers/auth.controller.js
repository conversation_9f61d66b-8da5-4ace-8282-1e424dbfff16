import passport from 'passport';
import { generateToken } from '../middlewares/auth.middlewares.js';
import { User, Role, Permission } from '../models/associations.js';
import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import logger from '../config/logger.config.js';
import authService from '../services/auth.service.js';
import { buildSuccessResponse } from '../utils/helpers.utils.js';
import tokenBlacklistService from '../services/tokenBlacklistService.js';
import jupyterService from '../services/jupyterhub.service.js';

let component, auditComponent;
/**
 * @desc    Google OAuth login
 * @route   GET /api/auth/google
 * @access  Public
 */
export const googleAuth = passport.authenticate('google', {
  scope: ['profile', 'email']
});

/**
 * @desc    Google OAuth callback
 * @route   GET /api/auth/google/callback
 * @access  Public
 */
export const googleCallback = asyncHandler(async (req, res, next) => {
  passport.authenticate('google', { session: false }, async (err, user) => {
    if (err) {
      logger.error('Google OAuth error:', err);
      return res.redirect(
        `${process.env.FRONTEND_URL}/login?error=oauth_error`
      );
    }

    if (!user) {
      return res.redirect(
        `${process.env.FRONTEND_URL}/login?error=authentication_failed`
      );
    }

    try {
      // Generate JWT token
      const token = generateToken(user);

      // Update last login
      await user.updateLastLogin();

      // Redirect to frontend with token
      res.redirect(`${process.env.FRONTEND_URL}/auth/callback?token=${token}`);
    } catch (error) {
      logger.error('Token generation error:', error);
      res.redirect(`${process.env.FRONTEND_URL}/login?error=token_error`);
    }
  })(req, res, next);
});

/**
 * @desc    Login with email and password
 * @route   POST /api/auth/login
 * @access  Public
 */
export const login = asyncHandler(
  async (req, res) => {
    component = 'login';
    auditComponent = 'User login';
    const result = await authService.login(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Login successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get current user profile
 * @route   GET /api/auth/me
 * @access  Private
 */
export const getCurrentUser = asyncHandler(async (req, res) => {
  const user = req.user;

  const userResponse = {
    id: user.id,
    name: user.name,
    email: user.email,
    profilePicture: user.profile_picture,
    lastLogin: user.last_login,
    status: user.status,
    preferences: user.preferences,
    roles:
      user.roles?.map(role => ({
        id: role.id,
        name: role.name,
        permissions: role.permissions?.map(permission => permission.key) || []
      })) || []
  };

  res.json({
    success: true,
    user: userResponse
  });
});

/**
 * @desc    Logout user
 * @route   POST /api/auth/logout
 * @access  Private
 */
export const logout = asyncHandler(async (req, res) => {
  try {
    const token = req.headers.authorization?.substring(7); // Remove 'Bearer ' prefix

    if (token) {
      // Add token to blacklist
      await tokenBlacklistService.blacklistToken(token);
      logger.info(`Token blacklisted for user: ${req.user.email}`);
    }

    logger.info(`User logged out: ${req.user.email}`);
    await jupyterService.stopServer(req.user);

    res.json({
      success: true,
      message: 'Logged out successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Logout error:', error);
    // Even if blacklisting fails, we still want to log the user out
    res.json({
      success: true,
      message: 'Logged out successfully',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * @desc    Logout from all devices
 * @route   POST /api/auth/logout-all
 * @access  Private
 */
export const logoutAllDevices = asyncHandler(async (req, res) => {
  try {
    const user = req.user;

    // In a more sophisticated implementation, you might:
    // 1. Store device-specific tokens in the database
    // 2. Invalidate all tokens for this user
    // 3. Force re-authentication on all devices

    // For now, we'll just log the action
    logger.info(`User logged out from all devices: ${user.email}`);

    // You could implement a user-specific blacklist or token versioning here
    // For example, increment a "token version" in the user record
    // and check this version in the JWT middleware

    res.json({
      success: true,
      message: 'Logged out from all devices successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Logout all devices error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to logout from all devices'
    });
  }
});

/**
 * @desc    Get logout statistics
 * @route   GET /api/auth/logout/stats
 * @access  Private (Admin only)
 */
export const getLogoutStats = asyncHandler(async (req, res) => {
  try {
    // Check if user has admin permissions
    const hasAdminPermission =
      req.userPermissions?.includes('admin:read') ||
      req.userRoles?.includes('admin');

    if (!hasAdminPermission) {
      return res.status(403).json({
        success: false,
        error: 'Access denied',
        message: 'Admin permission required'
      });
    }

    const stats = await tokenBlacklistService.getBlacklistStats();

    res.json({
      success: true,
      stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Get logout stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to get logout statistics'
    });
  }
});

/**
 * @desc    Refresh token
 * @route   POST /api/auth/refresh
 * @access  Private
 */
export const refreshToken = asyncHandler(async (req, res) => {
  const user = req.user;

  // Generate new token
  const newToken = generateToken(user);

  res.json({
    success: true,
    token: newToken
  });
});

/**
 * @desc    Update user preferences
 * @route   PATCH /api/auth/preferences
 * @access  Private
 */
export const updatePreferences = asyncHandler(async (req, res) => {
  const { preferences } = req.body;
  const user = req.user;

  if (!preferences || typeof preferences !== 'object') {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Valid preferences object is required'
    });
  }

  // Update user preferences
  await user.update({
    preferences: {
      ...user.preferences,
      ...preferences
    }
  });

  res.json({
    success: true,
    message: 'Preferences updated successfully',
    preferences: user.preferences
  });
});
