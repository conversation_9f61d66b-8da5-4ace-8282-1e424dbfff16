import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import checkpointService from '../services/checkpoint.service.js';
import { buildSuccessResponse } from '../utils/helpers.utils.js';
import httpStatus from 'http-status';

let component, auditComponent;
/**
 * @desc    Create a new checkpoint for a project
 * @route   POST /api/checkpoints
 * @access  Private (Instructor, Admin)
 */
export const createCheckpoint = asyncHandler(
  async (req, res) => {
    component = 'createCheckpoint';
    auditComponent = 'Create Check Point';
    const result = await checkpointService.createCheckpoint(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Checkpoint created successfully',
      component,
      auditComponent,
      httpStatus.CREATED
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get checkpoints for a project
 * @route   GET /api/projects/:id/checkpoints
 * @access  Private
 */
export const getProjectCheckpoints = asyncHandler(async (req, res) => {
  const { id: projectId } = req.params;
  const { includeGoals = 'true' } = req.query;

  const checkpoints = await checkpointService.getProjectCheckpoints(
    projectId,
    includeGoals === 'true'
  );

  res.json({
    success: true,
    checkpoints
  });
});

/**
 * @desc    Get checkpoint details
 * @route   GET /api/checkpoints/:id
 * @access  Private
 */
export const getCheckpointDetails = asyncHandler(async (req, res) => {
  const { id: checkpointId } = req.params;
  const userId = req.user.id;

  const checkpoint = await checkpointService.getCheckpointDetails(
    checkpointId,
    userId
  );

  if (!checkpoint) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Checkpoint not found'
    });
  }

  res.json({
    success: true,
    checkpoint
  });
});

/**
 * @desc    Update checkpoint progress
 * @route   PUT /api/checkpoints/:id/progress
 * @access  Private (Student)
 */
export const updateCheckpointProgress = asyncHandler(async (req, res) => {
  const { id: checkpointId } = req.params;
  const { projectId, updates } = req.body;

  if (!projectId) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Project ID is required'
    });
  }

  const progress = await checkpointService.updateCheckpointProgress(
    checkpointId,
    req.user.id,
    projectId,
    updates
  );

  res.json({
    success: true,
    message: 'Checkpoint progress updated successfully',
    progress
  });
});

/**
 * @desc    Submit checkpoint for review
 * @route   POST /api/checkpoints/:id/submit
 * @access  Private (Student)
 */
export const submitCheckpoint = asyncHandler(async (req, res) => {
  const { id: checkpointId } = req.params;
  const { projectId, files, notes } = req.body;

  if (!projectId) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Project ID is required'
    });
  }

  const submissionData = {
    files_submitted: files || [],
    student_notes: notes
  };

  const progress = await checkpointService.submitCheckpoint(
    checkpointId,
    req.user.id,
    projectId,
    submissionData
  );

  res.json({
    success: true,
    message: 'Checkpoint submitted successfully',
    progress
  });
});

/**
 * @desc    Grade a checkpoint
 * @route   POST /api/checkpoints/:id/grade
 * @access  Private (Instructor, Admin, TA)
 */
export const gradeCheckpoint = asyncHandler(async (req, res) => {
  const { id: checkpointProgressId } = req.params;
  const { rubricScores, totalScore, maxScore, feedback, detailedFeedback } =
    req.body;

  if (!totalScore || !maxScore) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Total score and max score are required'
    });
  }

  // Check if user has permission to grade
  const hasPermission =
    req.userRoles.includes('admin') ||
    req.userRoles.includes('instructor') ||
    req.userRoles.includes('ta');

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to grade checkpoints'
    });
  }

  const gradeData = {
    rubric_scores: rubricScores || {},
    total_score: parseFloat(totalScore),
    max_score: parseFloat(maxScore),
    feedback,
    detailed_feedback: detailedFeedback || {},
    evaluator_id: req.user.id
  };

  const grade = await checkpointService.gradeCheckpoint(
    checkpointProgressId,
    req.user.id,
    gradeData
  );

  res.json({
    success: true,
    message: 'Checkpoint graded successfully',
    grade
  });
});

/**
 * @desc    Get project progress for a student
 * @route   GET /api/projects/:id/progress
 * @access  Private
 */
export const getProjectProgress = asyncHandler(async (req, res) => {
  const { id: projectId } = req.params;
  const userId = req.query.userId || req.user.id;

  // Check if user has permission to view this progress
  const hasPermission =
    req.userRoles.includes('admin') || req.user.id === userId;

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view this progress'
    });
  }

  const progress = await checkpointService.calculateProjectProgress(
    projectId,
    userId
  );

  res.json({
    success: true,
    progress
  });
});

/**
 * @desc    Get instructor dashboard for a course
 * @route   GET /api/instructor/courses/:id/dashboard
 * @access  Private (Instructor, Admin)
 */
export const getInstructorDashboard = asyncHandler(async (req, res) => {
  const { id: courseId } = req.params;

  // Check if user has permission to view instructor dashboard
  const hasPermission =
    req.userRoles.includes('admin') || req.userRoles.includes('instructor');

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view instructor dashboard'
    });
  }

  const dashboard = await checkpointService.getInstructorDashboard(
    courseId,
    req.user.id
  );

  res.json({
    success: true,
    dashboard
  });
});

/**
 * @desc    Get student progress overview for a course
 * @route   GET /api/instructor/courses/:id/progress-overview
 * @access  Private (Instructor, Admin)
 */
export const getStudentProgressOverview = asyncHandler(async (req, res) => {
  const { id: courseId } = req.params;
  const { projectId } = req.query;

  // Check if user has permission to view progress overview
  const hasPermission =
    req.userRoles.includes('admin') || req.userRoles.includes('instructor');

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view progress overview'
    });
  }

  const overview = await checkpointService.getStudentProgressOverview(
    courseId,
    projectId
  );

  res.json({
    success: true,
    overview
  });
});

/**
 * @desc    Get checkpoint analytics
 * @route   GET /api/checkpoints/:id/analytics
 * @access  Private (Instructor, Admin)
 */
export const getCheckpointAnalytics = asyncHandler(async (req, res) => {
  const { id: checkpointId } = req.params;

  // Check if user has permission to view analytics
  const hasPermission =
    req.userRoles.includes('admin') || req.userRoles.includes('instructor');

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view checkpoint analytics'
    });
  }

  // This would be implemented in the service layer
  // For now, returning a placeholder
  res.json({
    success: true,
    message: 'Checkpoint analytics endpoint - to be implemented',
    checkpointId
  });
});

/**
 * @desc    Update checkpoint
 * @route   PUT /api/checkpoints/:id
 * @access  Private (Instructor, Admin)
 */
export const updateCheckpoint = asyncHandler(
  async (req, res) => {
    component = 'updateCheckpoint';
    auditComponent = 'update Check Point';
    const result = await checkpointService.updateCheckpoint(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Checkpoint Updated successfully',
      component,
      auditComponent,
      httpStatus.CREATED
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Delete checkpoint
 * @route   DELETE /api/checkpoints/:id
 * @access  Private (Instructor, Admin)
 */
export const deleteCheckpoint = asyncHandler(async (req, res) => {
  const { id: checkpointId } = req.params;

  // Check if user has permission to delete checkpoints
  const hasPermission =
    req.userRoles.includes('admin') || req.userRoles.includes('instructor');

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to delete checkpoints'
    });
  }

  // This would be implemented in the service layer
  // For now, returning a placeholder
  res.json({
    success: true,
    message: 'Checkpoint delete endpoint - to be implemented',
    checkpointId
  });
});
